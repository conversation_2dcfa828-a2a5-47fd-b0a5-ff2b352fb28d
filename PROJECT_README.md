# ShifaCare - مستشفى الشفاء الحديث

موقع إلكتروني شامل ومتجاوب لمستشفى الشفاء الحديث، مصمم باستخدام HTML5، CSS3، وJavaScript الخالص.

## 🏥 نظرة عامة

ShifaCare هو موقع إلكتروني حديث ومهني لمستشفى يقدم خدمات طبية شاملة. الموقع مصمم ليكون سهل الاستخدام، متجاوب مع جميع الأجهزة، ويدعم اللغة العربية بشكل كامل مع دعم RTL.

## ✨ المميزات

- **تصميم متجاوب**: يعمل بشكل مثالي على جميع الأجهزة (الهواتف، الأجهزة اللوحية، أجهزة الكمبيوتر)
- **دعم RTL**: دعم كامل للغة العربية مع تخطيط من اليمين إلى اليسار
- **تصميم حديث**: واجهة مستخدم عصرية وجذابة
- **سهولة التنقل**: قائمة تنقل واضحة ومنظمة
- **نماذج تفاعلية**: نماذج حجز المواعيد والتواصل مع التحقق من صحة البيانات
- **رسوم متحركة**: تأثيرات بصرية سلسة وجذابة
- **محسن للسرعة**: كود محسن لسرعة التحميل
- **متوافق مع SEO**: بنية محسنة لمحركات البحث

## 📁 هيكل المشروع

```
ShifaCare/
├── index.html              # الصفحة الرئيسية
├── about.html              # صفحة من نحن
├── services.html           # صفحة الخدمات
├── doctors.html            # صفحة الأطباء
├── contact.html            # صفحة اتصل بنا
├── css/
│   └── style.css          # ملف التنسيقات الرئيسي
├── js/
│   ├── script.js          # الوظائف الأساسية
│   ├── doctors-filter.js  # فلترة الأطباء
│   └── contact.js         # وظائف صفحة الاتصال
├── images/
│   └── README.md          # دليل الصور المطلوبة
└── README.md              # هذا الملف
```

## 🚀 البدء السريع

1. **تشغيل الموقع**:
   - افتح `index.html` في المتصفح
   - أو استخدم خادم محلي مثل Live Server

2. **إضافة الصور**:
   - راجع ملف `images/README.md` لمعرفة الصور المطلوبة
   - أضف الصور في مجلد `images/`

## 📱 الصفحات المكتملة

### ✅ 1. الصفحة الرئيسية (index.html)
- قسم البطل الرئيسي مع دعوة للعمل
- نظرة عامة عن المستشفى
- عرض الخدمات الرئيسية
- فريق الأطباء المميزين
- نموذج حجز موعد
- آراء المرضى
- معلومات التواصل

### ✅ 2. صفحة من نحن (about.html)
- قصة المستشفى
- الرؤية والرسالة والقيم
- إحصائيات المستشفى
- فريق القيادة
- المرافق والتجهيزات
- الشهادات والاعتمادات

### ✅ 3. صفحة الخدمات (services.html)
- عرض تفصيلي لجميع الخدمات الطبية
- معلومات الأسعار
- خدمات الطوارئ
- دعوة لحجز موعد

### ✅ 4. صفحة الأطباء (doctors.html)
- عرض شامل لجميع الأطباء
- فلترة حسب التخصص
- بحث في الأطباء
- معلومات تفصيلية لكل طبيب
- مواعيد العمل
- إمكانية حجز موعد

### ✅ 5. صفحة اتصل بنا (contact.html)
- معلومات التواصل الكاملة
- نموذج تواصل تفاعلي
- خريطة الموقع
- معلومات الطوارئ
- الأسئلة الشائعة

## 🎨 التصميم والألوان

### نظام الألوان:
- **الأساسي**: #2c5aa0 (أزرق طبي)
- **الثانوي**: #4CAF50 (أخضر طبيعي)
- **المميز**: #00BCD4 (أزرق فاتح)
- **النص الداكن**: #333333
- **النص الفاتح**: #666666
- **الخلفية**: #ffffff

### الخطوط:
- **الخط الأساسي**: Cairo (Google Fonts)
- **الأيقونات**: Font Awesome 6.4.0

## 💻 التقنيات المستخدمة

- **HTML5**: بنية دلالية حديثة
- **CSS3**: 
  - CSS Grid & Flexbox للتخطيط
  - CSS Custom Properties للمتغيرات
  - CSS Animations للحركات
  - Media Queries للاستجابة
- **JavaScript ES6+**:
  - DOM Manipulation
  - Event Handling
  - Form Validation
  - Intersection Observer API
  - Local Storage

## 📱 الاستجابة (Responsive Design)

الموقع محسن للعمل على:
- **الهواتف الذكية**: 320px - 768px
- **الأجهزة اللوحية**: 768px - 1024px
- **أجهزة الكمبيوتر**: 1024px وأعلى
- **الشاشات الكبيرة**: 1200px وأعلى

## 🔧 التخصيص

### تغيير الألوان:
```css
:root {
    --primary-color: #your-color;
    --secondary-color: #your-color;
    --accent-color: #your-color;
}
```

### إضافة خدمة جديدة:
1. أضف HTML في `services.html`
2. أضف التنسيقات في `style.css`
3. أضف الصورة في مجلد `images/`

### إضافة طبيب جديد:
1. أضف بطاقة الطبيب في `doctors.html`
2. أضف `data-category` للفلترة
3. أضف صورة الطبيب

## 🌐 دعم المتصفحات

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Internet Explorer 11 (دعم محدود)

## 📈 الأداء

- **سرعة التحميل**: محسن للسرعة
- **حجم الملفات**: مضغوط ومحسن
- **الصور**: محسنة للويب
- **CSS/JS**: مدمج ومحسن

## 🔒 الأمان

- تحقق من صحة النماذج
- حماية من XSS
- تشفير البيانات الحساسة
- HTTPS مطلوب للإنتاج

## 📞 الدعم والتواصل

للحصول على الدعم أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 11 123 4567

## 📝 حالة المشروع

### ✅ مكتمل:
- جميع الصفحات الخمس (HTML)
- التنسيقات الكاملة (CSS)
- الوظائف التفاعلية (JavaScript)
- التصميم المتجاوب
- دعم RTL للعربية
- نماذج التحقق والتفاعل
- فلترة وبحث الأطباء
- الأسئلة الشائعة التفاعلية

### 📋 المطلوب:
- إضافة الصور الحقيقية (راجع images/README.md)
- اختبار شامل على جميع الأجهزة
- تحسين SEO إضافي (اختياري)

---

**تم تطوير هذا الموقع بواسطة فريق ShifaCare التقني**

**الموقع جاهز للاستخدام! 🎉**
