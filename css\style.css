/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2c5aa0;
    --secondary-color: #4CAF50;
    --accent-color: #00BCD4;
    --text-dark: #333333;
    --text-light: #666666;
    --text-white: #ffffff;
    --bg-light: #f8f9fa;
    --bg-white: #ffffff;
    --border-color: #e0e0e0;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 5px 20px rgba(0, 0, 0, 0.15);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--bg-white);
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: var(--bg-white);
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: var(--transition);
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-logo i {
    margin-left: 10px;
    font-size: 1.8rem;
    color: var(--secondary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--text-white);
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    border: 2px solid transparent;
    cursor: pointer;
    text-align: center;
}

.btn-primary {
    background: var(--secondary-color);
    color: var(--text-white);
}

.btn-primary:hover {
    background: #45a049;
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-secondary {
    background: transparent;
    color: var(--text-white);
    border-color: var(--text-white);
}

.btn-secondary:hover {
    background: var(--text-white);
    color: var(--primary-color);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--text-white);
}

.hero-image {
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    background: var(--bg-light);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.about-text p {
    margin-bottom: 1.5rem;
    color: var(--text-light);
    line-height: 1.8;
}

.about-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-light);
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

/* Services Section */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--bg-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.service-icon i {
    font-size: 2rem;
    color: var(--text-white);
}

.service-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.service-description {
    color: var(--text-light);
    line-height: 1.6;
}

/* Doctors Section */
.doctors {
    background: var(--bg-light);
}

.doctors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.doctor-card {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.doctor-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.doctor-image {
    height: 250px;
    overflow: hidden;
}

.doctor-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.doctor-card:hover .doctor-image img {
    transform: scale(1.05);
}

.doctor-info {
    padding: 1.5rem;
    text-align: center;
}

.doctor-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.doctor-specialty {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.doctor-experience {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

/* Appointment Section */
.appointment {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--text-white);
}

.appointment .section-title,
.appointment .section-subtitle {
    color: var(--text-white);
}

.appointment-form-container {
    max-width: 600px;
    margin: 0 auto;
}

.appointment-form {
    background: var(--bg-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-dark);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    direction: rtl;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

/* Testimonials Section */
.testimonials {
    background: var(--bg-light);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: var(--bg-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.testimonial-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.testimonial-content p {
    font-style: italic;
    color: var(--text-light);
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.author-info h4 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.author-info span {
    color: var(--text-light);
    font-size: 0.9rem;
}

.testimonial-rating {
    color: #ffc107;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: var(--text-white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-logo i {
    margin-left: 10px;
    font-size: 1.8rem;
    color: var(--secondary-color);
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: var(--text-white);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #cccccc;
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--text-white);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--text-white);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contact-item i {
    color: var(--secondary-color);
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid #444;
    padding-top: 1rem;
    text-align: center;
    color: #cccccc;
}

/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--text-white);
    padding: 120px 0 80px;
    text-align: center;
}

.page-header-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.breadcrumb {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.breadcrumb a {
    color: var(--text-white);
    text-decoration: none;
    opacity: 0.8;
    transition: var(--transition);
}

.breadcrumb a:hover {
    opacity: 1;
}

.breadcrumb span {
    opacity: 0.6;
}

/* About Story Section */
.about-story {
    padding: 80px 0;
}

.about-story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.story-text h2 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.story-text p {
    margin-bottom: 1.5rem;
    color: var(--text-light);
    line-height: 1.8;
    font-size: 1.1rem;
}

.story-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

/* Vision Mission Values */
.vision-mission {
    background: var(--bg-light);
    padding: 80px 0;
}

.vmv-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.vmv-card {
    background: var(--bg-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.vmv-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.vmv-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.vmv-icon i {
    font-size: 2rem;
    color: var(--text-white);
}

.vmv-card h3 {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.vmv-card p {
    color: var(--text-light);
    line-height: 1.6;
}

.vmv-card ul {
    list-style: none;
    text-align: right;
}

.vmv-card ul li {
    color: var(--text-light);
    margin-bottom: 0.5rem;
    position: relative;
    padding-right: 1.5rem;
}

.vmv-card ul li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--secondary-color);
    font-weight: bold;
}

/* Statistics Section */
.statistics {
    padding: 80px 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: var(--bg-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.stat-icon i {
    font-size: 1.5rem;
    color: var(--text-white);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-light);
    font-size: 1rem;
}

/* Leadership Section */
.leadership {
    background: var(--bg-light);
    padding: 80px 0;
}

.leadership-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.leader-card {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.leader-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.leader-image {
    height: 250px;
    overflow: hidden;
}

.leader-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.leader-card:hover .leader-image img {
    transform: scale(1.05);
}

.leader-info {
    padding: 1.5rem;
}

.leader-info h3 {
    font-size: 1.3rem;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.leader-position {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.leader-description {
    color: var(--text-light);
    line-height: 1.6;
}

/* Facilities Section */
.facilities {
    padding: 80px 0;
}

.facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.facility-card {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.facility-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.facility-image {
    height: 200px;
    overflow: hidden;
}

.facility-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.facility-card:hover .facility-image img {
    transform: scale(1.05);
}

.facility-info {
    padding: 1.5rem;
}

.facility-info h3 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.facility-info p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Certifications Section */
.certifications {
    background: var(--bg-light);
    padding: 80px 0;
}

.certifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.cert-card {
    background: var(--bg-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.cert-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.cert-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.cert-icon i {
    font-size: 1.8rem;
    color: var(--text-white);
}

.cert-card h3 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.cert-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Services Detailed Grid */
.services-overview {
    padding: 80px 0;
}

.services-detailed-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.service-detailed-card {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.service-detailed-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.service-detailed-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    padding: 2rem;
    text-align: center;
}

.service-detailed-icon i {
    font-size: 3rem;
    color: var(--text-white);
}

.service-detailed-content {
    padding: 2rem;
}

.service-detailed-content h3 {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.service-detailed-content p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-features {
    list-style: none;
    margin-bottom: 1.5rem;
}

.service-features li {
    color: var(--text-light);
    margin-bottom: 0.5rem;
    position: relative;
    padding-right: 1.5rem;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--secondary-color);
    font-weight: bold;
}

.service-price {
    text-align: center;
    padding: 1rem;
    background: var(--bg-light);
    border-radius: var(--border-radius);
}

.service-price span {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Emergency Services Section */
.emergency-services {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--text-white);
    padding: 80px 0;
}

.emergency-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.emergency-text h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

.emergency-text p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.emergency-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.emergency-feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: var(--border-radius);
}

.emergency-feature i {
    font-size: 1.2rem;
    color: var(--text-white);
}

.emergency-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
}

/* Appointment CTA */
.appointment-cta {
    background: var(--bg-light);
    padding: 80px 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Doctors Filter */
.doctors-filter {
    background: var(--bg-light);
    padding: 2rem 0;
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: var(--bg-white);
    color: var(--text-dark);
    border: 2px solid var(--border-color);
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

/* Doctors Detailed Grid */
.doctors-section {
    padding: 80px 0;
}

.doctors-detailed-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.doctor-detailed-card {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.doctor-detailed-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.doctor-detailed-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.doctor-detailed-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.doctor-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(44, 90, 160, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.doctor-detailed-card:hover .doctor-overlay {
    opacity: 1;
}

.doctor-social {
    display: flex;
    gap: 1rem;
}

.doctor-social a {
    width: 50px;
    height: 50px;
    background: var(--text-white);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
}

.doctor-social a:hover {
    background: var(--secondary-color);
    color: var(--text-white);
    transform: translateY(-3px);
}

.doctor-detailed-info {
    padding: 2rem;
}

.doctor-detailed-name {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.doctor-detailed-specialty {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.doctor-credentials {
    margin-bottom: 1.5rem;
}

.credential-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-light);
    font-size: 0.9rem;
}

.credential-item i {
    color: var(--secondary-color);
    width: 16px;
}

.doctor-description {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.doctor-schedule {
    background: var(--bg-light);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
}

.doctor-schedule h4 {
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.doctor-schedule p {
    color: var(--text-light);
    margin: 0;
}

.doctor-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Why Choose Doctors */
.why-choose-doctors {
    background: var(--bg-light);
    padding: 80px 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--bg-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.feature-icon i {
    font-size: 1.8rem;
    color: var(--text-white);
}

.feature-card h3 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.feature-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Contact Information Section */
.contact-info-section {
    padding: 80px 0;
    background: var(--bg-light);
}

.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.contact-info-card {
    background: var(--bg-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.contact-info-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.contact-info-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.contact-info-icon i {
    font-size: 1.8rem;
    color: var(--text-white);
}

.contact-info-content h3 {
    font-size: 1.3rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.contact-info-content p {
    color: var(--text-light);
    line-height: 1.6;
}

.contact-info-content a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.contact-info-content a:hover {
    color: var(--accent-color);
}

/* Contact Form Section */
.contact-form-section {
    padding: 80px 0;
}

.contact-form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-form {
    background: var(--bg-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition);
    direction: rtl;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.privacy-link {
    color: var(--primary-color);
    text-decoration: none;
}

.privacy-link:hover {
    text-decoration: underline;
}

.btn-full {
    width: 100%;
    justify-content: center;
    gap: 0.5rem;
}

/* Map Container */
.map-container {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.map-container .section-header {
    padding: 2rem 2rem 0;
}

.map-wrapper {
    height: 400px;
    overflow: hidden;
}

.map-wrapper iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.map-info {
    padding: 2rem;
}

.map-info-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.map-info-item:last-child {
    margin-bottom: 0;
}

.map-info-item i {
    width: 40px;
    height: 40px;
    background: var(--bg-light);
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.map-info-item h4 {
    color: var(--text-dark);
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.map-info-item p {
    color: var(--text-light);
    margin: 0;
    font-size: 0.9rem;
}

/* Emergency Contact Section */
.emergency-contact {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: var(--text-white);
    padding: 80px 0;
}

.emergency-contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.emergency-contact-text h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.emergency-contact-text p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.emergency-numbers {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.emergency-number {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-white);
    text-align: center;
    transition: var(--transition);
    min-width: 180px;
}

.emergency-number:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
}

.emergency-number i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.emergency-number span {
    font-size: 1.2rem;
    font-weight: 600;
    display: block;
    margin-bottom: 0.25rem;
}

.emergency-number small {
    font-size: 0.9rem;
    opacity: 0.8;
}

.emergency-contact-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background: var(--bg-light);
}

.faq-grid {
    display: grid;
    gap: 1rem;
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.faq-question:hover {
    background: var(--bg-light);
}

.faq-question h3 {
    color: var(--text-dark);
    margin: 0;
    font-size: 1.1rem;
}

.faq-question i {
    color: var(--primary-color);
    transition: var(--transition);
}

.faq-item.active .faq-question i {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 1.5rem 1.5rem;
    max-height: 200px;
}

.faq-answer p {
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        max-width: 95%;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .services-detailed-grid,
    .doctors-detailed-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

@media (max-width: 992px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-text {
        order: 2;
    }

    .hero-image {
        order: 1;
    }

    .about-content,
    .story-content,
    .emergency-content,
    .emergency-contact-content,
    .contact-form-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .leadership-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .filter-buttons {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 1rem;
    }

    .filter-btn {
        white-space: nowrap;
        flex-shrink: 0;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--bg-white);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        z-index: 1000;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .nav-toggle {
        display: block;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .services-grid,
    .doctors-grid,
    .contact-info-grid {
        grid-template-columns: 1fr;
    }

    .services-detailed-grid,
    .doctors-detailed-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .emergency-numbers {
        flex-direction: column;
        align-items: center;
    }

    .emergency-number {
        width: 100%;
        max-width: 250px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
    }
}

@media (max-width: 576px) {
    .container {
        padding: 0 1rem;
    }

    .hero {
        padding: 60px 0;
    }

    .hero-content h1 {
        font-size: 1.8rem;
    }

    .page-header {
        padding: 60px 0;
    }

    .page-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .service-card,
    .doctor-card,
    .service-detailed-card,
    .doctor-detailed-card {
        margin-bottom: 1rem;
    }

    .service-detailed-content,
    .doctor-detailed-info {
        padding: 1.5rem;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .breadcrumb {
        font-size: 0.9rem;
    }

    .filter-buttons {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* Print Styles */
@media print {
    .header,
    .nav-toggle,
    .btn,
    .social-links,
    .footer {
        display: none !important;
    }

    .page-header {
        background: none !important;
        color: black !important;
    }

    .container {
        max-width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .section {
        page-break-inside: avoid;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --secondary-color: #008000;
        --accent-color: #800080;
        --text-dark: #000000;
        --text-light: #333333;
        --border-color: #000000;
    }

    .btn {
        border: 2px solid currentColor;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .scroll-to-top {
        transition: none;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--bg-white);
        width: 100%;
        text-align: center;
        transition: var(--transition);
        box-shadow: var(--shadow);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-stats {
        justify-content: center;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-buttons {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .about-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}
