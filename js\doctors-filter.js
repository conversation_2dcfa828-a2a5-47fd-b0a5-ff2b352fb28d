// Doctors Filter Functionality
document.addEventListener('DOMContentLoaded', function() {
    initDoctorsFilter();
});

function initDoctorsFilter() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const doctorCards = document.querySelectorAll('.doctor-detailed-card');

    // Add click event listeners to filter buttons
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filterValue = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter doctor cards
            filterDoctors(filterValue, doctorCards);
        });
    });
}

function filterDoctors(filterValue, doctorCards) {
    doctorCards.forEach(card => {
        const cardCategory = card.getAttribute('data-category');
        
        if (filterValue === 'all' || cardCategory === filterValue) {
            // Show card with animation
            card.style.display = 'block';
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            // Animate in
            setTimeout(() => {
                card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        } else {
            // Hide card with animation
            card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            card.style.opacity = '0';
            card.style.transform = 'translateY(-20px)';
            
            setTimeout(() => {
                card.style.display = 'none';
            }, 300);
        }
    });
    
    // Update results count
    updateResultsCount(filterValue, doctorCards);
}

function updateResultsCount(filterValue, doctorCards) {
    let visibleCount = 0;
    
    doctorCards.forEach(card => {
        const cardCategory = card.getAttribute('data-category');
        if (filterValue === 'all' || cardCategory === filterValue) {
            visibleCount++;
        }
    });
    
    // Create or update results counter
    let resultsCounter = document.querySelector('.results-counter');
    if (!resultsCounter) {
        resultsCounter = document.createElement('div');
        resultsCounter.className = 'results-counter';
        resultsCounter.style.cssText = `
            text-align: center;
            margin: 2rem 0;
            color: var(--text-light);
            font-size: 1rem;
        `;
        
        const doctorsSection = document.querySelector('.doctors-section .container');
        doctorsSection.insertBefore(resultsCounter, doctorsSection.querySelector('.doctors-detailed-grid'));
    }
    
    const filterText = getFilterText(filterValue);
    resultsCounter.textContent = `عرض ${visibleCount} ${visibleCount === 1 ? 'طبيب' : 'أطباء'} في تخصص ${filterText}`;
}

function getFilterText(filterValue) {
    const filterTexts = {
        'all': 'جميع التخصصات',
        'cardiology': 'القلب والأوعية الدموية',
        'gynecology': 'النساء والولادة',
        'surgery': 'الجراحة العامة',
        'pediatrics': 'طب الأطفال',
        'orthopedics': 'العظام والمفاصل',
        'internal': 'الطب الباطني'
    };
    
    return filterTexts[filterValue] || 'غير محدد';
}

// Search functionality for doctors
function initDoctorsSearch() {
    // Create search input if it doesn't exist
    let searchContainer = document.querySelector('.doctors-search');
    if (!searchContainer) {
        searchContainer = document.createElement('div');
        searchContainer.className = 'doctors-search';
        searchContainer.style.cssText = `
            text-align: center;
            margin: 2rem 0;
        `;
        
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = 'ابحث عن طبيب...';
        searchInput.className = 'search-input';
        searchInput.style.cssText = `
            width: 100%;
            max-width: 400px;
            padding: 12px 20px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            direction: rtl;
            transition: var(--transition);
        `;
        
        searchContainer.appendChild(searchInput);
        
        const filterSection = document.querySelector('.doctors-filter .container');
        filterSection.appendChild(searchContainer);
        
        // Add search functionality
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            searchDoctors(searchTerm);
        });
        
        // Focus styles
        searchInput.addEventListener('focus', function() {
            this.style.borderColor = 'var(--primary-color)';
            this.style.boxShadow = '0 0 0 3px rgba(44, 90, 160, 0.1)';
        });
        
        searchInput.addEventListener('blur', function() {
            this.style.borderColor = 'var(--border-color)';
            this.style.boxShadow = 'none';
        });
    }
}

function searchDoctors(searchTerm) {
    const doctorCards = document.querySelectorAll('.doctor-detailed-card');
    let visibleCount = 0;
    
    doctorCards.forEach(card => {
        const doctorName = card.querySelector('.doctor-detailed-name').textContent.toLowerCase();
        const doctorSpecialty = card.querySelector('.doctor-detailed-specialty').textContent.toLowerCase();
        const doctorDescription = card.querySelector('.doctor-description').textContent.toLowerCase();
        
        const isMatch = doctorName.includes(searchTerm) || 
                       doctorSpecialty.includes(searchTerm) || 
                       doctorDescription.includes(searchTerm);
        
        if (searchTerm === '' || isMatch) {
            card.style.display = 'block';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
            visibleCount++;
        } else {
            card.style.display = 'none';
            card.style.opacity = '0';
            card.style.transform = 'translateY(-20px)';
        }
    });
    
    // Update search results counter
    updateSearchResults(searchTerm, visibleCount);
}

function updateSearchResults(searchTerm, visibleCount) {
    let searchResults = document.querySelector('.search-results');
    if (!searchResults) {
        searchResults = document.createElement('div');
        searchResults.className = 'search-results';
        searchResults.style.cssText = `
            text-align: center;
            margin: 1rem 0;
            color: var(--text-light);
            font-size: 0.9rem;
        `;
        
        const doctorsSection = document.querySelector('.doctors-section .container');
        doctorsSection.insertBefore(searchResults, doctorsSection.querySelector('.doctors-detailed-grid'));
    }
    
    if (searchTerm) {
        searchResults.textContent = `تم العثور على ${visibleCount} ${visibleCount === 1 ? 'نتيجة' : 'نتائج'} للبحث عن "${searchTerm}"`;
        searchResults.style.display = 'block';
    } else {
        searchResults.style.display = 'none';
    }
}

// Initialize search functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add a small delay to ensure all elements are rendered
    setTimeout(() => {
        initDoctorsSearch();
    }, 100);
});

// Smooth scroll to doctors section when filter is clicked
function scrollToDoctors() {
    const doctorsSection = document.querySelector('.doctors-section');
    if (doctorsSection) {
        const headerHeight = document.querySelector('.header').offsetHeight;
        const targetPosition = doctorsSection.offsetTop - headerHeight - 20;
        
        window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
        });
    }
}

// Add scroll functionality to filter buttons
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Small delay to allow filter animation to start
            setTimeout(scrollToDoctors, 100);
        });
    });
});

// Add loading animation for doctor cards
function addLoadingAnimation() {
    const doctorCards = document.querySelectorAll('.doctor-detailed-card');
    
    doctorCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        
        // Stagger the animation
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Initialize loading animation
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(addLoadingAnimation, 200);
});
